// import http from './http';
import { ws } from './websocket';
import { SendFunctionCode } from '../enum';
import type { LoginResponse } from '../types/code';
import { createLogger } from '../libs/logger';

const logger = createLogger('LoginService');

class LoginService {
  /**
   * WebSocket方式登录
   * @param username 用户名
   * @param password 密码
   * @returns Promise 登录成功时resolve用户信息
   */
  static async loginByWS(username: string, password: string) {
    logger.info('开始WebSocket登录', { username });
    // md5
    // const md5p = await Utils.md5(password);
    return ws.register(
      SendFunctionCode.登录,
      {
        loginType: 1,
        branchId: '0',
        orgId: '0',
        credit: 0,
        username,
        password,
        mac: '',
        os: '',
        md5: '',
        sessionId: '0',
        configStr: '',
      },
      6,
    ) as Promise<LoginResponse>;
  }

  /**
   * WebSocket方式登出
   */
  static logoutByWS() {
    logger.info('开始WebSocket登出');
    return ws.register(SendFunctionCode.登出);
  }
}

export default LoginService;
