<script setup lang="ts">
import type { SellSetting, ProfitLossSetting } from '@/types';

const { modelValue, disabled } = defineProps<{
  modelValue: SellSetting['profitLoss'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['profitLoss']];
}>();

// 更新基础配置
const updateBasicValue = (key: keyof SellSetting['profitLoss'], value: any) => {
  const newValue = { ...modelValue };
  (newValue as any)[key] = value;
  emit('update:modelValue', newValue);
};

// 更新止盈止损配置
const updateProfitLossValue = (
  type: 'takeProfit' | 'StopLoss',
  index: number,
  key: keyof ProfitLossSetting,
  value: any,
) => {
  const newValue = { ...modelValue };
  const newArray = [...newValue[type]];
  newArray[index] = { ...newArray[index], [key]: value };
  newValue[type] = newArray as any;
  emit('update:modelValue', newValue);
};

// 止盈止损配置项
const profitLossConfigs = [
  { type: 'takeProfit', title: '止盈', direction: '上涨', key: 'profit' },
  { type: 'StopLoss', title: '止损', direction: '下跌', key: 'loss' },
] as const;
</script>

<template>
  <div flex="~ col" gap-4 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <!-- 基础配置 -->
    <div>
      <div flex aic gap-8>
        <div flex aic gap-8>
          <div>卖出价格=最新价+</div>
          <ElInputNumber
            flex-1
            min-w-1
            :model-value="modelValue.pricePercent"
            :min="-20"
            :max="20"
            @update:model-value="val => updateBasicValue('pricePercent', val)"
          >
            <template #suffix>%</template>
          </ElInputNumber>
        </div>
        <div>
          <ElCheckbox
            :model-value="modelValue.open"
            @update:model-value="val => updateBasicValue('open', val)"
          >
            启动撤单重单
          </ElCheckbox>
        </div>
        <div flex aic gap-8>
          <div>撤单间隔</div>
          <ElInputNumber
            flex-1
            min-w-1
            :model-value="modelValue.time"
            :min="0"
            :max="1000000"
            @update:model-value="val => updateBasicValue('time', val)"
          >
            <template #suffix>毫秒</template>
          </ElInputNumber>
        </div>
      </div>
    </div>

    <!-- 止盈止损配置 -->
    <div flex="~ row" gap-8>
      <div v-for="config in profitLossConfigs" :key="config.key" flex="~ col 1" gap-10>
        <div mb-4>{{ config.title }}</div>
        <div
          v-for="(item, index) in modelValue[config.type]"
          :key="`${config.key}-${index}`"
          flex="~ none"
          aic
          gap-4
        >
          <ElCheckbox
            style="margin-right: 10px"
            :model-value="item.open"
            @update:model-value="val => updateProfitLossValue(config.type, index, 'open', val)"
          >
            启动{{ config.title + (index + 1) }}
          </ElCheckbox>
          <label>{{ config.direction }}</label>
          <ElInputNumber
            :model-value="item.amplitude"
            :min="-20"
            :max="20"
            style="width: 110px"
            @update:model-value="val => updateProfitLossValue(config.type, index, 'amplitude', val)"
          >
            <template #suffix>%</template>
          </ElInputNumber>
          <label>则卖出仓位</label>
          <ElInputNumber
            :model-value="item.positionPercent"
            :min="0"
            :max="100"
            style="width: 110px"
            @update:model-value="
              val => updateProfitLossValue(config.type, index, 'positionPercent', val)
            "
          >
            <template #suffix>%</template>
          </ElInputNumber>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number {
  width: 100%;
}
</style>
