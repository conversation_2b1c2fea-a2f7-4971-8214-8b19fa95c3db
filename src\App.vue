<script setup lang="ts">
import { RouterView } from 'vue-router';
import { defineAsyncComponent, ref, computed } from 'vue';
import { Misc } from '@/script';
import { ElConfigProvider, type MessageConfigContext } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import router from '@/router';

const AppHeader = defineAsyncComponent(() => import('./components/electron/AppHeader.vue'));

const message = ref<MessageConfigContext>({
  offset: Misc.isElectron() ? 50 : 16,
});

const showHeader = computed(() => {
  return Misc.isElectron() && router.currentRoute.value.name !== 'floating';
});
</script>

<template>
  <ElConfigProvider size="small" :message :locale="zhCn">
    <AppHeader v-if="showHeader" />
    <RouterView flex-1 min-h-1 />
  </ElConfigProvider>
</template>

<style scoped></style>
