import type { FormPool, Pool, PoolDetail, StrategyLog } from '@/types';
import http from './http';
import { createLogger } from '../libs/logger';

const logger = createLogger('PoolService');

class PoolService {
  static base = '/v5/strategy';

  /**
   * 查询所有策略池
   */
  static getStrategyPools() {
    return http<Pool[]>(this.base);
  }

  /**
   * 添加策略池
   * @param pool - 策略池
   */
  static addStrategyPool(pool: FormPool) {
    return http<void>(this.base, {
      method: 'POST',
      data: pool,
    });
  }

  /**
   * 更新策略池，需要包含 id 字段
   * @param pool - 策略池
   */
  static updateStrategyPool(pool: Pool) {
    return http<void>(this.base, {
      method: 'PUT',
      data: pool,
    });
  }

  /**
   * 删除策略池
   * @param id - 策略池ID
   */
  static deleteStrategyPool(id: number) {
    return http<void>(this.base, {
      method: 'DELETE',
      params: {
        id,
      },
    });
  }

  /**
   * 启动策略池
   * @param id - 策略池ID，不传则启动所有策略池
   */
  static startStrategyPool(pool_id?: number) {
    logger.info('启动策略池', { pool_id: pool_id || '全部' });
    return http<void>(`${this.base}/start`, {
      params: {
        pool_id,
      },
    });
  }

  /**
   * 停止策略池
   * @param id - 策略池ID，不传则停止所有策略池
   */
  static stopStrategyPool(pool_id?: number) {
    logger.info('停止策略池', { pool_id: pool_id || '全部' });
    return http<void>(`${this.base}/stop`, {
      params: {
        pool_id,
      },
    });
  }

  /**
   * 检查策略池明细，检查股票是否已在某个策略池中
   * @param instrument - 股票代码
   */
  static checkPoolDetail(instrument: string) {
    return http<void>(`${this.base}/detail/check`, {
      params: {
        instrument,
      },
    });
  }

  /**
   * 查询策略池明细
   * @param id - 策略池ID
   */
  static getPoolDetail(pool_id: number) {
    return http<PoolDetail[]>(`${this.base}/detail`, {
      params: {
        pool_id,
      },
    });
  }

  /**
   * 添加策略池明细
   * @param detail - 策略池明细
   */
  static addPoolDetail(detail: Omit<PoolDetail, 'id' | 'status'>) {
    return http<void>(`${this.base}/detail`, {
      method: 'POST',
      data: detail,
    });
  }

  /**
   * 更新策略池明细，需要包含 id 字段
   * @param detail - 策略池明细
   */
  static updatePoolDetail(detail: PoolDetail) {
    return http<void>(`${this.base}/detail`, {
      method: 'PUT',
      data: detail,
    });
  }

  /**
   * 删除策略池明细
   * @param id - 策略池明细ID
   */
  static deletePoolDetail(id: number) {
    return http<void>(`${this.base}/detail`, {
      method: 'DELETE',
      params: {
        id,
      },
    });
  }

  /**
   * 批量添加策略池明细
   * @param details - 策略池明细数组
   */
  static addPoolDetails(details: Array<Omit<PoolDetail, 'id' | 'status'>>) {
    return http<void>(`${this.base}/detail/batch`, {
      method: 'POST',
      data: details,
      params: {
        pool_id: details[0].poolId,
      },
    });
  }

  /**
   * 清空策略池明细
   * @param pool_id - 策略池ID
   */
  static clearPoolDetails(pool_id: number) {
    return http<void>(`${this.base}/detail/clear`, {
      params: {
        pool_id,
      },
    });
  }

  /**
   * 更新策略池明细启停状态
   * @param id - 策略池明细ID
   * @param start - 启停状态
   */
  static updatePoolDetailStatus(id: number, start: boolean) {
    return http<void>(`${this.base}/detail/status`, {
      params: {
        id,
        start,
      },
    });
  }

  /**
   * 查询所有策略池明细
   */
  static getAllPoolDetails() {
    return http<PoolDetail[]>(`${this.base}/detail/all`, {
      method: 'POST',
      data: [],
    });
  }

  /**
   * 查询策略日志
   * @param instrument - 股票代码
   */
  static getStrategyLogs(instrument: string) {
    return http<StrategyLog[]>(`/api/strategy/logs`, {
      params: {
        instrument,
      },
    });
  }
}

export default PoolService;
