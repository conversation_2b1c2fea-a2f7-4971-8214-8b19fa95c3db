<script setup lang="tsx">
import { onBeforeUnmount, onMounted, shallowRef } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { TablePositionInfo, ColumnDefinition } from '@/types';
import { Utils, Misc } from '@/script';
import { RecordService } from '@/api';
import { TacticStatusEnum } from '@/enum';
import { ElMessage } from 'element-plus';
import { createLogger } from '@/libs/logger';

const logger = createLogger('SellPositionTable');

const emit = defineEmits<{
  positionSelect: [position: TablePositionInfo];
}>();

let interval = 0;

// 响应式数据
const positions = shallowRef<TablePositionInfo[]>([]);
const selectedRowIndex = shallowRef(-1);

// 处理行点击事件
const handleRowClick = (row: TablePositionInfo, index: number) => {
  selectedRowIndex.value = index;
  emit('positionSelect', row);
};

// 持仓列表表格列定义
const positionColumns: ColumnDefinition<TablePositionInfo> = [
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'risePercent',
    dataKey: 'risePercent',
    title: '涨跌幅',
    width: 160,
    cellRenderer: ({ rowData }) => {
      // 计算涨跌幅 (浮动盈亏 / 市值) * 100
      const risePercent =
        rowData.marketValue > 0 ? (rowData.dayProfit / rowData.marketValue) * 100 : 0;
      const color =
        risePercent > 0 ? 'c-[var(--g-red)]' : risePercent < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return (
        <span class={color}>
          {Utils.formatNumber(risePercent, { fix: 2, prefix: true, default: '--' })}%
        </span>
      );
    },
  },
  {
    key: 'todayPosition',
    dataKey: 'todayPosition',
    title: '持仓数量',
    width: 160,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'yesterdayPosition',
    dataKey: 'yesterdayPosition',
    title: '可用数量',
    width: 160,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '仓位',
    width: 120,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { default: '--' })}%</span>,
  },
  {
    key: 'dayProfit',
    dataKey: 'dayProfit',
    title: '浮动盈亏',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const color =
        cellData > 0 ? 'c-[var(--g-red)]' : cellData < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return (
        <span class={color}>
          {Utils.formatNumber(cellData, { fix: 2, prefix: true, separator: true })}
        </span>
      );
    },
  },
  {
    key: 'profitRatio',
    dataKey: 'profitRatio',
    title: '盈亏比',
    width: 140,
    cellRenderer: ({ rowData }) => {
      // 计算盈亏比 (浮动盈亏 / 市值) * 100
      const profitRatio =
        rowData.marketValue > 0 ? (rowData.dayProfit / rowData.marketValue) * 100 : 0;
      const color =
        profitRatio > 0 ? 'c-[var(--g-red)]' : profitRatio < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return <span class={color}>{Utils.formatNumber(profitRatio, { fix: 2 })}%</span>;
    },
  },
  {
    key: 'sellStrategy',
    dataKey: 'sellStrategy',
    title: '启停',
    width: 200,
    cellRenderer: ({ rowData }: { rowData: TablePositionInfo }) => {
      const isPositionStopped =
        rowData.status === TacticStatusEnum.新建 ||
        rowData.status === TacticStatusEnum.已停止 ||
        rowData.status === TacticStatusEnum.已删除 ||
        rowData.status === TacticStatusEnum.已完成;
      return (
        <el-switch
          modelValue={isPositionStopped ? TacticStatusEnum.新建 : TacticStatusEnum.运行中}
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
        />
      );
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '策略状态',
    width: 160,
    cellRenderer: ({ rowData }) => {
      const statusText = rowData.status === TacticStatusEnum.运行中 ? '运行中' : '已停止';
      const color = rowData.status === TacticStatusEnum.运行中 ? 'c-[var(--g-green)]' : 'c-gray';
      return <span class={color}>{statusText}</span>;
    },
  },
  {
    key: 'progress',
    dataKey: 'progress',
    title: '执行进度',
    width: 160,
    cellRenderer: ({ cellData }) => (
      <div pos-relative>
        <div
          pos-absolute
          left-0
          top-0
          bottom-0
          class="bg-[--g-green]"
          op-20
          style={{ width: `${cellData}%` }}
        />
        <span pos-relative>{Utils.formatNumber(cellData, { fix: 2 })}%</span>
      </div>
    ),
  },
];

// 获取持仓列表
const fetchPositions = async () => {
  const { errorCode, data } = await RecordService.getPositions();
  if (errorCode === 0 && data) {
    positions.value = data;
  }
};

// 启停操作
const beforeChange = async (rowData: TablePositionInfo) => {
  const isPositionStopped =
    rowData.status === TacticStatusEnum.新建 ||
    rowData.status === TacticStatusEnum.已停止 ||
    rowData.status === TacticStatusEnum.已删除 ||
    rowData.status === TacticStatusEnum.已完成;

  const action = isPositionStopped ? '启动' : '停止';
  logger.info(`${action}卖出策略`, {
    instrument: rowData.instrument,
    instrumentName: rowData.instrumentName,
  });

  // 如果没有卖出策略ID，则无法启停
  if (!rowData.sellStrategy?.id) {
    ElMessage.warning('该持仓尚未配置卖出策略，无法启停');
    return false;
  }

  const { errorCode, errorMsg } = await RecordService[
    isPositionStopped ? 'startSellStrategy' : 'stopSellStrategy'
  ](rowData.sellStrategy.id);

  if (errorCode === 0) {
    // 更新本地数据
    Misc.putRow(
      {
        ...rowData,
        status: isPositionStopped ? TacticStatusEnum.运行中 : TacticStatusEnum.已停止,
      },
      positions,
    );
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

// 一键启动
const handleStartAll = async () => {
  logger.info('一键启动所有卖出策略');
  const { errorCode, errorMsg } = await RecordService.startSellStrategy();
  if (errorCode === 0) {
    ElMessage.success('启动成功');
    fetchPositions(); // 刷新数据
  } else {
    ElMessage.error(errorMsg || '启动失败');
  }
};

// 一键停止
const handleStopAll = async () => {
  logger.info('一键停止所有卖出策略');
  const { errorCode, errorMsg } = await RecordService.stopSellStrategy();
  if (errorCode === 0) {
    ElMessage.success('停止成功');
    fetchPositions(); // 刷新数据
  } else {
    ElMessage.error(errorMsg || '停止失败');
  }
};

// 刷新数据的方法，供父组件调用
const refreshData = () => {
  fetchPositions();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPositions();
  interval = window.setInterval(fetchPositions, 5000);
});

onBeforeUnmount(() => {
  clearInterval(interval);
});

// 暴露方法给父组件
defineExpose({
  refreshData,
});
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic jcsb px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>持仓</span>
      <div>
        <el-button color="var(--g-bg-green)" @click="handleStartAll">
          <i mr-4 fs-14 i-mdi-motion-play-outline />
          一键启动
        </el-button>
        <el-button color="var(--g-red)" @click="handleStopAll">
          <i mr-4 fs-14 i-mdi-motion-pause-outline />
          一键停止
        </el-button>
      </div>
    </div>
    <div flex="~ 1" min-h-0>
      <VirtualizedTable
        w-full
        identity="instrument"
        :data="positions"
        :columns="positionColumns"
        :selected-index="selectedRowIndex"
        @row-click="handleRowClick"
      />
    </div>
  </div>
</template>

<style scoped></style>
