<script setup lang="ts">
import type { RiskControl } from '@/types';
import { ref, onMounted } from 'vue';
import { ElMessage, ElButton, ElInputNumber } from 'element-plus';
import { RiskService } from '@/api';
import { createLogger } from '@/libs/logger';

const logger = createLogger('RiskParams');

const loading = ref(false);
const saving = ref(false);

const formData = ref<RiskControl>({
  id: 0,
  maxDrawDown: null,
  mainBoardPosition: null,
  starMarketPosition: null,
  maxPosition: null,
  buyTime: null,
  buyMoney: null,
  sellMoney: null,
  sellTime: null,
});

const originalData = ref<RiskControl | null>(null);

const leftColumnItems = [
  { label: '最大回撤比例', key: 'maxDrawDown' },
  { label: '主板最大个股仓位', key: 'mainBoardPosition' },
  { label: '创业科创最大个股仓位', key: 'starMarketPosition' },
  { label: '账户最大总仓位', key: 'maxPosition' },
] as const;

const rightColumnItems = [
  { label: '买入', key: 'buyMoney', key2: 'buyTime' },
  { label: '卖出', key: 'sellMoney', key2: 'sellTime' },
] as const;

const loadData = async () => {
  try {
    loading.value = true;
    const { errorCode, data } = await RiskService.getRiskControl();
    if (errorCode === 0 && data) {
      formData.value = { ...data };
      originalData.value = { ...data };
    }
  } catch (error) {
    ElMessage.error('加载风控参数失败');
    logger.error('加载风控参数失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleSave = async () => {
  try {
    saving.value = true;
    await RiskService.updateRiskControl(formData.value);
    ElMessage.success('保存成功');
    originalData.value = { ...formData.value };
  } catch (error) {
    ElMessage.error('保存失败');
    logger.error('保存风控参数失败:', error);
  } finally {
    saving.value = false;
  }
};

const handleCancel = () => {
  if (originalData.value) {
    formData.value = { ...originalData.value };
  }
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div>
    <div bg="[--g-panel-bg]" h-32 flex aic px-16 jcsb border-b="1 solid [--el-border-color]">
      <div text-14 font-bold>风控参数配置</div>
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button color="var(--g-primary)" @click="handleSave">保存</el-button>
      </div>
    </div>

    <div flex="~ col" gap-10 mt-10>
      <div v-for="item in leftColumnItems" :key="item.key" flex aic gap-10>
        <label flex jce w-150>{{ item.label }}</label>
        <ElInputNumber v-model="formData[item.key]" :min="0" :max="100" :precision="0">
          <template #suffix>%</template>
        </ElInputNumber>
      </div>
    </div>

    <!-- 分割线 -->
    <div w-full h-1 border-b="1 solid [--el-border-color]" my-10></div>

    <div px-16>
      <div text-14 font-bold mb-10>重点监控股票池</div>
      <div v-for="item in rightColumnItems" :key="item.key" flex aic gap-10 mb-10>
        <ElInputNumber style="width: 90px" v-model="formData[item.key2]" :min="0" :precision="0" />
        <span>秒内单股最多{{ item.label }}委托</span>
        <ElInputNumber v-model="formData[item.key]" :min="0" :precision="0" />
        <span>万元股票，超出则拒绝</span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
