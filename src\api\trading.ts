import type { TradeOrderInfo } from '@/types';
import http from './http';
import { createLogger } from '../libs/logger';

const logger = createLogger('TradingService');

class TradingService {
  /**
   * 撤销订单
   */
  static cancelOrder(orderId: number | string) {
    logger.info('撤销订单', { orderId });
    return http<void>('/api/cancelOrder', {
      method: 'POST',
      data: {
        orderId,
      },
    });
  }

  /**
   * 普通下单
   */
  static sendOrder(order: TradeOrderInfo) {
    logger.info('发送订单', {
      instrument: order.instrument,
      direction: order.bsFlag,
      volume: order.volume,
      price: order.price,
    });
    return http<void>('/api/sendOrder', {
      method: 'POST',
      data: order,
    });
  }

  /**
   * 订阅订单变化
   */
  static subscribeOrderChange(callback: () => void) {}

  /**
   * 取消订阅订单变化
   */
  static unsubscribeOrderChange(callback: () => void) {}

  /**
   * 订阅成交变化
   */
  static subscribeTradeChange(callback: () => void) {}

  /**
   * 取消订阅成交变化
   */
  static unsubscribeTradeChange(callback: () => void) {}

  /**
   * 订阅持仓变化
   */
  static subscribePositionChange(callback: () => void) {}

  /**
   * 取消订阅持仓变化
   */
  static unsubscribePositionChange(callback: () => void) {}
}

export default TradingService;
