import { ipcMain } from 'electron';
import type { WriteLogParams } from '../../shared/electron-api-types';
import { LogFileManager } from './log-manager';
import WindowManager from './window-manager';
import StockCaptureService from './stock-capture';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('IPCHandlers');

export class IPCHandlers {
  private logFileManager: LogFileManager;
  private windowManager: WindowManager;
  // TODO: 手动触发捕获用，继承三方库后不需要在此引入
  private stockCaptureService: StockCaptureService;

  constructor(logFileManager: LogFileManager, windowManager: WindowManager) {
    this.logFileManager = logFileManager;
    this.windowManager = windowManager;
    this.stockCaptureService = StockCaptureService.getInstance((stockCode: string) => {
      const floatingWindow = windowManager?.getFloatingWindow();
      if (floatingWindow) {
        floatingWindow.webContents.send('stock-code-captured', stockCode);
      }
    });
    this.setupHandlers();
  }

  private setupHandlers(): void {
    this.setupWindowHandlers();
    this.setupLogHandlers();
    this.setupStockCaptureHandlers();
    this.setupDataSyncHandlers();
  }

  private setupWindowHandlers(): void {
    // 主窗口控制
    ipcMain.on('window-minimize', () => {
      this.windowManager.minimizeMainWindow();
    });

    ipcMain.on('window-close', () => {
      this.windowManager.closeMainWindow();
      // 主窗口关闭时，悬浮窗口也会在 closeMainWindow 中被关闭
    });

    ipcMain.on('window-maximize', (_, maximize?: boolean) => {
      this.windowManager.maximizeMainWindow(maximize);
    });
  }

  private setupLogHandlers(): void {
    // 日志写入处理
    ipcMain.handle('write-log', async (_, params: WriteLogParams) => {
      try {
        await this.logFileManager.writeLog(params);
        return { success: true };
      } catch (error) {
        logger.error('处理日志写入请求失败', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });
  }

  private setupStockCaptureHandlers(): void {
    // 手动触发捕获
    ipcMain.handle('trigger-capture', () => {
      this.stockCaptureService.triggerCapture();
    });
  }

  private setupDataSyncHandlers(): void {
    // 用户登录状态变化
    ipcMain.on('user-login-status-changed', (_, isLoggedIn: boolean) => {
      this.windowManager.setUserLoggedIn(isLoggedIn);
      logger.info('用户登录状态变化', { isLoggedIn });
    });

    // 悬浮窗口请求一次全量数据
    ipcMain.on('sync-all-data', () => {
      const mainWindow = this.windowManager.getMainWindow();
      if (!mainWindow) return;
      mainWindow.webContents.send('sync-all-data');
    });

    // 从主窗口向悬浮窗口推送股票池数据
    ipcMain.on('sync-pools-to-floating', (_, pools: any[]) => {
      this.windowManager.sendDataToFloatingWindow('pools-data-updated', pools);
      logger.debug('向悬浮窗口同步股票池数据', { poolCount: pools.length });
    });

    // 从主窗口向悬浮窗口推送股票池详情数据
    ipcMain.on('sync-pool-details-to-floating', (_, poolDetails: any[]) => {
      this.windowManager.sendDataToFloatingWindow('pool-details-data-updated', poolDetails);
      logger.debug('向悬浮窗口同步股票池详情数据', { detailCount: poolDetails.length });
    });

    // 股票池状态变化通知
    ipcMain.on('pool-status-changed', (_, data: { poolId: number; status: any }) => {
      this.windowManager.sendDataToFloatingWindow('pool-status-changed', data);
      logger.debug('股票池状态变化通知', data);
    });

    // 股票池详情状态变化通知
    ipcMain.on('pool-detail-status-changed', (_, data: { detailId: number; status: any }) => {
      this.windowManager.sendDataToFloatingWindow('pool-detail-status-changed', data);
      logger.debug('股票池详情状态变化通知', data);
    });
  }

  /**
   * 清理所有IPC处理器
   */
  cleanup(): void {
    // 移除所有IPC监听器
    ipcMain.removeAllListeners('window-minimize');
    ipcMain.removeAllListeners('window-close');
    ipcMain.removeAllListeners('window-maximize');
    ipcMain.removeAllListeners('user-login-status-changed');
    ipcMain.removeAllListeners('sync-pools-to-floating');
    ipcMain.removeAllListeners('sync-pool-details-to-floating');
    ipcMain.removeAllListeners('pool-status-changed');
    ipcMain.removeAllListeners('pool-detail-status-changed');

    logger.info('IPC处理器清理完成');
  }
}

export default IPCHandlers;
