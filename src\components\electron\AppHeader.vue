<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import { createLogger } from '@/libs/logger';

const logger = createLogger('AppHeader');

const isMaximized = ref(false);

const buttons = [
  {
    action: 'minimize',
    icon: 'i-mdi-window-minimize',
    handler: () => window.ipcRenderer.send('window-minimize'),
  },
  {
    action: 'maximize',
    icon: (isMaximized: boolean) =>
      isMaximized ? 'i-mdi-window-restore' : 'i-mdi-window-maximize',
    handler: () => window.ipcRenderer.send('window-maximize'),
  },
  {
    action: 'close',
    icon: 'i-mdi-window-close',
    handler: () => window.ipcRenderer.send('window-close'),
  },
];

const windowStateChange = (event: Electron.IpcRendererEvent, max: boolean) => {
  logger.debug('窗口状态变化:', { maximized: max });
  isMaximized.value = max;
};

onMounted(() => {
  // 监听窗口状态变化
  window.ipcRenderer.on('window-state-changed', windowStateChange);
});

onUnmounted(() => {
  // 取消监听窗口状态变化
  window.ipcRenderer.off('window-state-changed', windowStateChange);
});
</script>

<template>
  <div class="app-header" z-1000 flex aic jcsb h-30 bg-dark>
    <div class="title" pl-20 c-white fs-16>爱建策略交易系统</div>
    <div class="controls" h-full z-1000 flex aic>
      <div
        v-for="btn in buttons"
        :key="btn.action"
        flex
        aic
        jcc
        w-40
        fs-14
        h-full
        hover="bg-[--g-bg-hover2]"
        c-white
        cursor-pointer
        @click="btn.handler"
      >
        <i
          :class="typeof btn.icon === 'function' ? btn.icon(isMaximized) : btn.icon"
          :block="btn.action === 'minimize'"
        ></i>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-header {
  -webkit-app-region: drag;
  .controls {
    -webkit-app-region: no-drag;
  }
}
</style>
