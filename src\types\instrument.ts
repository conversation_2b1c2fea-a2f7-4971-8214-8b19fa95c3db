/** 合约数据结构 */
export interface InstrumentInfo {
  /** 资产类型 */
  assetType: number;
  /** 是否为融资融券标的（0和1） */
  creditBuy: number;
  /** 交易所ID */
  exchangeId: string;
  /** 到期日期 */
  expireDate: string;
  /** 合约代码 */
  instrument: string;
  /** 合约代码(带交易所前缀) */
  instrument1: string;
  /** 合约名称 */
  instrumentName: string;
  /** 是否为抵押品标志（0和1） */
  isCollateral: number;
  /** 最新价格 */
  lastPrice: number;
  /** 涨停价 */
  upperLimitPrice: number;
  /** 跌停价 */
  lowerLimitPrice: number;
  /** 期权类型 */
  optionType: number;
  /** 昨收盘价 */
  preClosePrice: number;
  /** 价格最小变动单位 */
  priceTick: number;
  /** 行权价 */
  strikePrice: number;
  /** 潜在指向合约代码 */
  underlyingInstrument: string;
  /** 成交量乘数 */
  volumeMultiple: number;
}
