import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useInstrumentStore } from '@/stores';
import { extractStockCode, createInstrumentMap, validateStockCodes } from '@/utils/instrument';
import type { InstrumentInfo } from '@/types';
import * as XLSX from 'xlsx';
import { createLogger } from '@/libs/logger';

const logger = createLogger('StockImport');

/**
 * 股票导入配置接口
 */
export interface StockImportConfig<T = any> {
  /** 获取现有股票列表的函数 */
  getExistingStocks: () => T[];
  /** 从现有股票中提取股票代码的函数 */
  extractExistingCode: (stock: T) => string;
  /** 将有效股票转换为目标格式的函数 */
  transformToTarget: (instruments: InstrumentInfo[]) => any[];
  /** 执行批量添加的函数 */
  batchAdd: (items: any[]) => Promise<{ errorCode: number; errorMsg?: string }>;
  /** 导入成功后的回调函数 */
  onSuccess?: () => void;
  /** 导入前的验证函数，返回 true 继续导入，false 或错误信息则停止 */
  preValidate?: () => boolean | string;
}

/**
 * 股票导入 composable
 */
export const useStockImport = () => {
  const isImporting = ref(false);
  const instrumentStore = useInstrumentStore();

  /**
   * 下载导入模板
   */
  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = '/股票导入模板.xlsx';
    link.download = '股票导入模板.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  /**
   * 从Excel文件中提取股票代码
   */
  const extractStockCodesFromFile = async (file: File): Promise<string[]> => {
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

    if (jsonData.length < 2) {
      throw new Error('模板文件格式不正确，至少需要包含标题行和数据行');
    }

    const stockCodes: string[] = [];
    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (row && row[0]) {
        const code = extractStockCode(row[0]);
        if (code) {
          stockCodes.push(code);
        }
      }
    }

    if (stockCodes.length === 0) {
      throw new Error('未找到有效的股票代码');
    }

    return stockCodes;
  };

  /**
   * 处理股票导入
   */
  const handleImport = async <T>(config: StockImportConfig<T>) => {
    // 导入前验证
    if (config.preValidate) {
      const validateResult = config.preValidate();
      if (validateResult !== true) {
        ElMessage.error(typeof validateResult === 'string' ? validateResult : '验证失败');
        return;
      }
    }

    // 确保已获取全量股票列表
    const allInstruments = await instrumentStore.getStockInstruments();

    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';

    input.onchange = async event => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        isImporting.value = true;

        // 提取股票代码
        const stockCodes = await extractStockCodesFromFile(file);

        // 构建股票代码到InstrumentInfo的映射
        const instrumentMap = createInstrumentMap(allInstruments);

        // 过滤有效的股票并排除已存在的
        const existingStocks = config.getExistingStocks();
        const existingCodes = new Set(
          existingStocks
            .map(stock => extractStockCode(config.extractExistingCode(stock)))
            .filter((code): code is string => Boolean(code)),
        );

        const { validInstruments, invalidCodes } = validateStockCodes(
          stockCodes,
          instrumentMap,
          existingCodes,
        );

        if (validInstruments.length === 0) {
          if (invalidCodes.length > 0) {
            ElMessage.error(`所有股票代码都无效或已存在。无效代码：${invalidCodes.join(', ')}`);
          } else {
            ElMessage.warning('所有股票都已存在于当前股票池中');
          }
          return;
        }

        // 转换为目标格式
        const targetItems = config.transformToTarget(validInstruments);

        // 执行批量添加
        const { errorCode, errorMsg } = await config.batchAdd(targetItems);
        if (errorCode === 0) {
          ElMessage.success('导入成功');
          config.onSuccess?.();
        } else if (errorCode === 10003) {
          ElMessage.warning(errorMsg);
        } else {
          ElMessage.error(errorMsg || '导入失败');
        }
      } catch (error) {
        logger.error('股票导入失败:', error);
        if (error instanceof Error) {
          ElMessage.error(error.message);
        } else {
          ElMessage.error('文件解析失败，请检查文件格式');
        }
      } finally {
        isImporting.value = false;
      }
    };

    input.click();
  };

  return {
    isImporting,
    downloadTemplate,
    handleImport,
  };
};
