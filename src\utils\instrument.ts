import type { InstrumentInfo } from '@/types';

/**
 * 提取股票代码的工具函数
 * @param code 原始代码字符串
 * @returns 提取的6位股票代码，如果无效则返回null
 */
export const extractStockCode = (code: string): string | null => {
  if (!code) return null;

  // 移除所有空格和引号
  const cleanCode = code.toString().replace(/[\s'"]/g, '');

  // 匹配6位数字的股票代码
  const match = cleanCode.match(/(\d{6})/);
  return match ? match[1] : null;
};

/**
 * 创建股票代码到InstrumentInfo的映射
 * @param instruments 股票列表
 * @returns Map对象，key为6位股票代码，value为InstrumentInfo
 */
export const createInstrumentMap = (instruments: InstrumentInfo[]): Map<string, InstrumentInfo> => {
  const instrumentMap = new Map<string, InstrumentInfo>();
  instruments.forEach((instrument: InstrumentInfo) => {
    const code = extractStockCode(instrument.instrument);
    if (code) {
      instrumentMap.set(code, instrument);
    }
  });
  return instrumentMap;
};

/**
 * 验证股票代码列表的有效性
 * @param stockCodes 股票代码列表
 * @param instrumentMap 股票映射表
 * @param existingCodes 已存在的股票代码集合
 * @returns 验证结果，包含有效股票列表和无效代码列表
 */
export const validateStockCodes = (
  stockCodes: string[],
  instrumentMap: Map<string, InstrumentInfo>,
  existingCodes: Set<string>,
): {
  validInstruments: InstrumentInfo[];
  invalidCodes: string[];
} => {
  const validInstruments: InstrumentInfo[] = [];
  const invalidCodes: string[] = [];

  stockCodes.forEach(code => {
    if (existingCodes.has(code)) {
      // 已存在，跳过
      return;
    }

    const instrument = instrumentMap.get(code);
    if (instrument) {
      validInstruments.push(instrument);
    } else {
      invalidCodes.push(code);
    }
  });

  return { validInstruments, invalidCodes };
};
