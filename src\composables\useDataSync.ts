import PoolService from '@/api/pool';
import { createLogger } from '@/libs/logger';
import { onMounted, onUnmounted } from 'vue';

const logger = createLogger('DataSync');

export function useDataSync() {
  // 同步股票池数据到悬浮窗口
  const syncPoolsData = async () => {
    try {
      const { errorCode, data } = await PoolService.getStrategyPools();
      if (errorCode === 0 && Array.isArray(data)) {
        window.ipcRenderer.send('sync-pools-to-floating', data);
        logger.debug('同步股票池数据到悬浮窗口', { poolCount: data.length });
      }
    } catch (error) {
      logger.error('同步股票池数据失败', error);
    }
  };

  // 同步股票池详情数据到悬浮窗口
  const syncPoolDetailsData = async () => {
    try {
      const { errorCode, data } = await PoolService.getAllPoolDetails();
      if (errorCode === 0 && Array.isArray(data)) {
        window.ipcRenderer.send('sync-pool-details-to-floating', data);
        logger.debug('同步股票池详情数据到悬浮窗口', { detailCount: data.length });
      }
    } catch (error) {
      logger.error('同步股票池详情数据失败', error);
    }
  };

  // 同步所有数据
  const syncAllData = async () => {
    await Promise.all([syncPoolsData(), syncPoolDetailsData()]);
  };

  // 通知股票池状态变化
  const notifyPoolStatusChanged = (poolId: number, status: any) => {
    window.dataSync?.notifyPoolStatusChanged(poolId, status);
    logger.debug('通知股票池状态变化', { poolId, status });
  };

  // 通知股票池详情状态变化
  const notifyPoolDetailStatusChanged = (detailId: number, status: any) => {
    window.dataSync?.notifyPoolDetailStatusChanged(detailId, status);
    logger.debug('通知股票池详情状态变化', { detailId, status });
  };

  onMounted(() => {
    window.ipcRenderer.on('sync-all-data', syncAllData);
  });

  onUnmounted(() => {
    window.ipcRenderer.off('sync-all-data', syncAllData);
  });

  return {
    syncPoolsData,
    syncPoolDetailsData,
    syncAllData,
    notifyPoolStatusChanged,
    notifyPoolDetailStatusChanged,
  };
}
