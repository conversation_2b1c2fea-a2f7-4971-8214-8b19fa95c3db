<script setup lang="tsx">
import { onBeforeUnmount, onMounted, shallowRef } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { OrderDetail, ColumnDefinition } from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';

let interval = 0;

// 响应式数据
const orders = shallowRef<OrderDetail[]>([]);

// 卖出委托表格列定义
const orderColumns: ColumnDefinition<OrderDetail> = [
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'orderTime',
    dataKey: 'orderTime',
    title: '委托时间',
    width: 200,
    cellRenderer: ({ cellData }) => {
      if (!cellData) return <span>--</span>;
      const time = new Date(cellData).toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
      return <span>{time}</span>;
    },
  },
  {
    key: 'orderPrice',
    dataKey: 'orderPrice',
    title: '委托价格',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { fix: 2 })}</span>,
  },
  {
    key: 'volumeOriginal',
    dataKey: 'volumeOriginal',
    title: '委托数量',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'tradedVolume',
    dataKey: 'tradedVolume',
    title: '成交数量',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价格',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { fix: 2 })}</span>,
  },
  {
    key: 'orderStatus',
    dataKey: 'orderStatus',
    title: '委托状态',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const statusMap: Record<number, { text: string; color: string }> = {
        0: { text: '未报', color: 'c-gray' },
        1: { text: '待报', color: 'c-yellow' },
        2: { text: '已报', color: 'c-blue' },
        3: { text: '已报待撤', color: 'c-orange' },
        4: { text: '部分成交', color: 'c-[var(--g-green)]' },
        5: { text: '全部成交', color: 'c-[var(--g-green)]' },
        6: { text: '已撤', color: 'c-gray' },
        7: { text: '部撤', color: 'c-orange' },
        8: { text: '已拒绝', color: 'c-[var(--g-red)]' },
      };
      const status = statusMap[cellData] || { text: '未知', color: 'c-gray' };
      return <span class={status.color}>{status.text}</span>;
    },
  },
];

// 获取卖出委托详情
const fetchOrders = async () => {
  try {
    const { errorCode, data } = await RecordService.getOrderDetail(1); // 1表示卖出方向
    if (errorCode === 0 && data) {
      // 将单个OrderDetail转换为数组格式
      orders.value = data;
    } else {
      orders.value = [];
    }
  } catch (error) {
    orders.value = [];
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders();
  interval = window.setInterval(fetchOrders, 5000);
});

onBeforeUnmount(() => {
  clearInterval(interval);
});
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>卖出委托</span>
    </div>
    <div flex="~ 1" min-h-0>
      <VirtualizedTable w-full :data="orders" :columns="orderColumns" />
    </div>
  </div>
</template>

<style scoped></style>
