import { ipc<PERSON><PERSON><PERSON>, contextBridge } from 'electron';
import { Ip<PERSON><PERSON><PERSON><PERSON>, DataSyncAPI } from '../shared/electron-api-types';

const api: Ipc<PERSON><PERSON><PERSON> = {
  on: ipcRenderer.on.bind(ipc<PERSON>enderer),
  emit: ipc<PERSON>enderer.emit.bind(ipc<PERSON>enderer),
  off: ipc<PERSON>enderer.off.bind(ipc<PERSON>enderer),
  send: ipc<PERSON>enderer.send,
  invoke: ipcRenderer.invoke,
};

const dataSyncAPI: DataSyncAPI = {
  notifyPoolStatusChanged: (poolId: number, status: any) =>
    ipcRenderer.send('pool-status-changed', { poolId, status }),
  notifyPoolDetailStatusChanged: (detailId: number, status: any) =>
    ipcRenderer.send('pool-detail-status-changed', { detailId, status }),

  onPoolStatusChanged: (callback: (data: { poolId: number; status: any }) => void) => {
    ipcRenderer.on('pool-status-changed', (_, data: { poolId: number; status: any }) =>
      callback(data),
    );
  },
  onPoolDetailStatusChanged: (callback: (data: { detailId: number; status: any }) => void) => {
    ipcRenderer.on('pool-detail-status-changed', (_, data: { detailId: number; status: any }) =>
      callback(data),
    );
  },
};

contextBridge.exposeInMainWorld('ipcRenderer', api);
contextBridge.exposeInMainWorld('dataSync', dataSyncAPI);
