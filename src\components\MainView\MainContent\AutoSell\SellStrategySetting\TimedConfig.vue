<script setup lang="ts">
import { ElInputNumber, ElSelect, ElOption } from 'element-plus';
import type { SellSetting } from '@/types';
import { BuyLevelsEnum } from '@/enum';

const { modelValue, disabled } = defineProps<{
  modelValue: SellSetting['timed'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['timed']];
}>();

// 更新数据
const updateValue = (key: keyof SellSetting['timed'], value: any) => {
  const newValue = { ...modelValue };
  (newValue as any)[key] = value;
  emit('update:modelValue', newValue);
};

// 配置项定义
const configItems = [
  { key: 'strategyDelayTime' as const, label: '时间间隔', suffix: '秒', min: 0 },
  { key: 'strategyVolume' as const, label: '单笔数量', suffix: '股', min: 0 },
  { key: 'cancelProtectedTime' as const, label: '未成撤单', suffix: '秒', min: 0 },
  { key: 'positionPercent' as const, label: '总仓位', suffix: '%', min: 0, max: 100 },
];

// 档位选项
const levelOptions = [
  { label: '买一', value: BuyLevelsEnum.买一 },
  { label: '买二', value: BuyLevelsEnum.买二 },
  { label: '买三', value: BuyLevelsEnum.买三 },
  { label: '买四', value: BuyLevelsEnum.买四 },
  { label: '买五', value: BuyLevelsEnum.买五 },
];
</script>

<template>
  <div flex="~ col" gap-4 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <div flex="~ wrap" aic gap-10>
      <div flex aic gap-4 v-for="item in configItems" :key="item.key">
        <label>{{ item.label }}</label>
        <ElInputNumber
          style="width: 120px"
          :model-value="modelValue[item.key]"
          :min="item.min"
          :max="item.max"
          size="small"
          @update:model-value="val => updateValue(item.key, val)"
        >
          <template #suffix>{{ item.suffix }}</template>
        </ElInputNumber>
      </div>
      <div flex aic gap-4>
        <label>档位</label>
        <ElSelect
          style="width: 80px"
          :model-value="modelValue.priceFollowType"
          size="small"
          @update:model-value="val => updateValue('priceFollowType', val)"
        >
          <ElOption
            v-for="option in levelOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number,
.el-select {
  width: 100%;
}
</style>
