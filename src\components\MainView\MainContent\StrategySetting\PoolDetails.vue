<script setup lang="tsx">
import type {
  ColumnDefinition,
  InstrumentInfo,
  PoolDetail,
  RowAction,
  WsPoolDetail,
} from '@/types';
import { ref, useTemplateRef, watch } from 'vue';
import { PoolService } from '@/api';
import { usePoolSelectionStore } from '@/stores';
import { useWebSocketPush } from '@/composables/useWebSocketPush';
import { storeToRefs } from 'pinia';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { AssetTypeEnum, TacticStatusEnum } from '@/enum';
import { ElMessage, ElMessageBox } from 'element-plus';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import { useStockImport, type StockImportConfig } from '@/composables/useStockImport';
import { Utils } from '@/script';

// 表格列定义
const columns: ColumnDefinition<PoolDetail> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'positionRate',
    dataKey: 'positionRate',
    title: '仓位',
    width: 200,
    cellRenderer: ({ cellData }) => {
      return <span>{Utils.formatNumber(cellData, { default: '--' })}%</span>;
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const text = TacticStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
];

const rowActions: RowAction<PoolDetail>[] = [
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { errorCode, errorMsg } = await PoolService.deletePoolDetail(row.id);
        if (errorCode === 0) {
          ElMessage.success('删除成功');
          // 不再手动删除，等待WebSocket推送更新
        } else {
          ElMessage.error(errorMsg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const details = ref<PoolDetail[]>([]);
const { selectedPool } = storeToRefs(usePoolSelectionStore());
const selectedInstrument = ref<InstrumentInfo>();
const positionRate = ref(5);
const instrumentInputRef = useTemplateRef('instrumentInputRef');
const { onPoolDetailUpdate } = useWebSocketPush();

// 导入相关变量
const { isImporting, downloadTemplate, handleImport: handleStockImport } = useStockImport();

// 监听股票池详情推送更新
onPoolDetailUpdate((detail: WsPoolDetail) => {
  // 只处理当前选中股票池的详情更新
  if (!selectedPool.value || detail.poolId !== selectedPool.value.id) {
    return;
  }

  if (detail.deleted === 1) {
    // 删除股票池详情
    details.value = details.value.filter(x => x.id !== detail.id);
  } else {
    // 新增或更新股票池详情
    const existingIndex = details.value.findIndex(x => x.id === detail.id);
    if (existingIndex >= 0) {
      // 更新现有详情
      details.value[existingIndex] = detail;
    } else {
      // 新增详情
      details.value.push(detail);
    }
  }
});

// 获取股票池详情数据
const getDetails = async (poolId: number) => {
  const { errorCode, data } = await PoolService.getPoolDetail(poolId);
  if (errorCode === 0 && data) {
    details.value = data;
  } else {
    details.value = [];
  }
};

// 监听选中股票池变化
watch(
  selectedPool,
  newPool => {
    if (newPool) {
      getDetails(newPool.id);
    } else {
      details.value = [];
    }
  },
  { immediate: true },
);

watch(selectedInstrument, val => {
  if (val) {
    if (!details.value.some(x => x.instrument === val.instrument1)) {
      handleAdd();
    }
  }
});

// 股票导入配置
const importConfig: StockImportConfig<PoolDetail> = {
  getExistingStocks: () => details.value,
  extractExistingCode: stock => stock.instrument,
  transformToTarget: instruments =>
    instruments.map(instrument => ({
      instrument: instrument.instrument1,
      instrumentName: instrument.instrumentName,
      poolName: selectedPool.value!.groupName,
      poolId: selectedPool.value!.id,
      positionRate: positionRate.value,
    })),
  batchAdd: items => PoolService.addPoolDetails(items),
  preValidate: () => {
    if (!selectedPool.value) {
      return '请先选择股票池';
    }
    return true;
  },
};

/** 手动添加股票 */
const handleAdd = async () => {
  if (!selectedPool.value) {
    ElMessage.error('请先选择股票池');
    return;
  }
  if (!selectedInstrument.value) {
    ElMessage.error('请先选择股票');
    return;
  }
  if (positionRate.value <= 0) {
    ElMessage.error('请先设置仓位');
    return;
  }
  const detail: Omit<PoolDetail, 'id' | 'status'> = {
    instrument: selectedInstrument.value.instrument1,
    instrumentName: selectedInstrument.value.instrumentName,
    poolName: selectedPool.value.groupName,
    poolId: selectedPool.value.id,
    positionRate: positionRate.value,
  };
  instrumentInputRef.value?.clear();
  const { errorCode, errorMsg } = await PoolService.addPoolDetail(detail);
  if (errorCode === 0) {
    ElMessage.success('添加成功');
    // 不再手动调用getDetails()，等待WebSocket推送更新
  } else {
    ElMessage.error(errorMsg || '添加失败');
  }
};

// 处理导入
const handleImport = () => handleStockImport(importConfig);

const handleClear = () => {
  if (!selectedPool.value) {
    ElMessage.error('请先选择股票池');
    return;
  }
  ElMessageBox.confirm('确认清空当前股票池的所有股票？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await PoolService.clearPoolDetails(selectedPool.value!.id);
    if (errorCode === 0) {
      ElMessage.success('清空成功');
      // 不再手动调用getDetails()，等待WebSocket推送更新
    } else {
      ElMessage.error(errorMsg || '清空失败');
    }
  });
};
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 jcsb>
      <div w-380 flex aic gap-10>
        <div text-14 font-bold>股票池详情</div>
        <InstrumentInput
          flex-1
          min-w-1
          v-model="selectedInstrument"
          :assetType="AssetTypeEnum.股票"
          placeholder="添加股票"
          ref="instrumentInputRef"
        />
        <div flex aic gap-10>
          <div>股票池仓位</div>
          <el-input v-model="positionRate" class="position-input">
            <template #append>%</template>
          </el-input>
        </div>
      </div>
      <div flex aic>
        <el-tooltip effect="light" content="下载导入模板" placement="top">
          <i cursor-pointer mr-10 block fs-18 i-mdi-download @click="downloadTemplate"></i>
        </el-tooltip>
        <el-button color="var(--g-primary)" :loading="isImporting" @click="handleImport">
          <i mr-4 fs-14 i-mdi-import />
          导入
        </el-button>
        <el-button color="var(--g-red)" @click="handleClear">
          <i mr-4 fs-14 i-mdi-trash-can-outline />
          清空
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      showIndex
      :data="details"
      :columns="columns"
      :row-actions="rowActions"
    ></VirtualizedTable>
  </div>
</template>

<style scoped>
.position-input {
  width: 70px;
}
:deep() {
  .el-input-group__append {
    padding: 0 5px;
  }
}
</style>
