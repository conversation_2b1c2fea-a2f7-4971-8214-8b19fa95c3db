import type { RiskStockTypeEnum } from '@/enum';

/** 风控股票 */
export interface RiskStock {
  /** ID */
  id: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 列表类型 */
  listType: RiskStockTypeEnum;
}

/** 风控设置 */
export interface RiskControl {
  /** ID */
  id: number;
  /** 最大回撤比例(%) */
  maxDrawDown: number | null;
  /** 主板最大个股仓位(%) */
  mainBoardPosition: number | null;
  /** 创业科创最大个股仓位(%) */
  starMarketPosition: number | null;
  /** 账户最大总仓位(%) */
  maxPosition: number | null;
  /** 买入时间(秒) */
  buyTime: number | null;
  /** 买入金额(万元) */
  buyMoney: number | null;
  /** 卖出金额(万元) */
  sellMoney: number | null;
  /** 卖出时间(秒) */
  sellTime: number | null;
}
