<script setup lang="ts">
import TopBar from '@/components/MainView/TopBar.vue';
import MainContent from '@/components/MainView/MainContent.vue';
import MainTabs from '@/components/MainView/MainTabs.vue';
import { useTemplateRef } from 'vue';
import { useDataSync } from '@/composables/useDataSync';

const mainTabsRef = useTemplateRef('mainTabsRef');

// 启动数据同步服务
useDataSync();
</script>

<template>
  <div h-full flex="~ col">
    <TopBar />
    <MainTabs ref="mainTabsRef" />
    <MainContent flex-1 min-h-1 :active-tab="mainTabsRef?.activeName" />
  </div>
</template>

<style scoped></style>
