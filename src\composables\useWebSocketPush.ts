import { onMounted, onUnmounted } from 'vue';
import { ReceiveFunctionCode } from '@/enum';
import type { EventDetail, WsPool, WsPoolDetail } from '@/types';
import { createLogger } from '@/libs/logger';

const logger = createLogger('WebSocketPush');

/**
 * WebSocket推送消息处理composable
 */
export const useWebSocketPush = () => {
  const poolUpdateCallbacks = new Set<(pools: WsPool<0 | 1>[]) => void>();
  const poolDetailUpdateCallbacks = new Set<(detail: WsPoolDetail) => void>();

  /**
   * 注册股票池更新回调
   * @param callback 回调函数
   */
  const onPoolUpdate = (callback: (pools: WsPool<0 | 1>[]) => void) => {
    poolUpdateCallbacks.add(callback);

    // 返回取消注册的函数
    return () => {
      poolUpdateCallbacks.delete(callback);
    };
  };

  /**
   * 注册股票池详情更新回调
   * @param callback 回调函数
   */
  const onPoolDetailUpdate = (callback: (detail: WsPoolDetail) => void) => {
    poolDetailUpdateCallbacks.add(callback);

    // 返回取消注册的函数
    return () => {
      poolDetailUpdateCallbacks.delete(callback);
    };
  };

  /**
   * 处理WebSocket推送消息
   * @param event 事件对象
   */
  const handleWebSocketPush = (event: Event) => {
    if (!('detail' in event)) {
      logger.error('收到无效的WebSocket推送事件', event);
      return;
    }

    const customEvent = event as CustomEvent<EventDetail<ReceiveFunctionCode>>;
    const { fc, body } = customEvent.detail;

    switch (fc) {
      case ReceiveFunctionCode.新增策略池推送:
      case ReceiveFunctionCode.更新策略池推送:
      case ReceiveFunctionCode.删除策略池推送:
        // 通知所有股票池更新回调
        poolUpdateCallbacks.forEach(callback => {
          try {
            callback([body as WsPool<0 | 1>]);
          } catch (error) {
            logger.error('股票池更新回调执行错误:', error);
          }
        });
        break;

      case ReceiveFunctionCode.策略池明细变更推送:
        // 通知所有股票池详情更新回调
        poolDetailUpdateCallbacks.forEach(callback => {
          try {
            callback(body as WsPoolDetail);
          } catch (error) {
            logger.error('股票池详情更新回调执行错误:', error);
          }
        });
        break;

      default:
        logger.warn('未处理的推送消息类型:', fc);
    }
  };

  onMounted(() => {
    window.addEventListener('websocket-push', handleWebSocketPush as EventListener);
  });

  onUnmounted(() => {
    window.removeEventListener('websocket-push', handleWebSocketPush as EventListener);
    // 清理所有回调
    poolUpdateCallbacks.clear();
    poolDetailUpdateCallbacks.clear();
  });

  return {
    onPoolUpdate,
    onPoolDetailUpdate,
  };
};
