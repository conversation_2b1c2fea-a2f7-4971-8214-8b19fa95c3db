<script setup lang="tsx">
import type { ColumnDefinition, InstrumentInfo, RiskStock, RowAction } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { shallowRef, onMounted, ref, computed, watch, useTemplateRef } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { RiskService } from '@/api';
import { RiskStockTypeEnum, AssetTypeEnum } from '@/enum';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import { useStockImport, type StockImportConfig } from '@/composables/useStockImport';
import { createLogger } from '@/libs/logger';

const logger = createLogger('StockList');

interface Props {
  listType: RiskStockTypeEnum;
  title: string;
}

const { listType, title } = defineProps<Props>();

const tableData = shallowRef<RiskStock[]>([]);
const loading = ref(false);
const instrumentInputRef = useTemplateRef('instrumentInputRef');
const selectedInstrument = ref<InstrumentInfo>();

// 导入相关变量
const { isImporting, downloadTemplate, handleImport: handleStockImport } = useStockImport();

const columns: ColumnDefinition<RiskStock> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 150,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 150,
  },
];

const rowActions: RowAction<RiskStock>[] = [
  {
    label: '删除',
    onClick: row => {
      handleRemoveStock(row.id);
    },
    color: 'var(--g-red)',
  },
];

const listTypeName = computed(() => {
  return listType === RiskStockTypeEnum.黑名单 ? '黑名单' : '重点监控名单';
});

watch(selectedInstrument, val => {
  if (val) {
    if (!tableData.value.some(x => x.instrument === val.instrument1)) {
      handleAdd();
    }
  }
});

const loadData = async () => {
  try {
    loading.value = true;
    const response = await RiskService.getStockList(listType);
    tableData.value = response.data || [];
  } catch (error) {
    ElMessage.error(`加载${listTypeName.value}失败`);
    logger.error(`加载${listTypeName.value}失败:`, error);
  } finally {
    loading.value = false;
  }
};

/** 手动添加股票 */
const handleAdd = async () => {
  if (!selectedInstrument.value) {
    ElMessage.error('请先选择股票');
    return;
  }

  const detail: Omit<RiskStock, 'id'> = {
    instrument: selectedInstrument.value.instrument1,
    instrumentName: selectedInstrument.value.instrumentName,
    listType,
  };
  instrumentInputRef.value?.clear();
  const { errorCode, errorMsg } = await RiskService.addStock(listType, detail);
  if (errorCode === 0) {
    ElMessage.success('添加成功');
    loadData();
  } else {
    ElMessage.error(errorMsg || '添加失败');
  }
};

const handleRemoveStock = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要移除该股票吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await RiskService.removeStock(id);
    ElMessage.success('移除成功');
    await loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败');
      logger.error('移除股票失败:', error);
    }
  }
};

onMounted(() => {
  loadData();
});

// 股票导入配置
const importConfig: StockImportConfig<RiskStock> = {
  getExistingStocks: () => tableData.value,
  extractExistingCode: stock => stock.instrument,
  transformToTarget: instruments =>
    instruments.map(instrument => ({
      instrument: instrument.instrument1,
      instrumentName: instrument.instrumentName,
      listType,
    })),
  batchAdd: items => RiskService.batchAddStocks(listType, items),
  onSuccess: () => loadData(),
};

// 处理导入
const handleImport = () => handleStockImport(importConfig);

const handleClear = () => {
  ElMessageBox.confirm('确认清空当前股票池的所有股票？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await RiskService.deleteStockList(listType);
    if (errorCode === 0) {
      ElMessage.success('清空成功');
      loadData();
    } else {
      ElMessage.error(errorMsg || '清空失败');
    }
  });
};
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 jcsb>
      <div w-280 flex aic gap-10>
        <div text-14 font-bold>{{ title }}</div>
        <InstrumentInput
          flex-1
          min-w-1
          v-model="selectedInstrument"
          :assetType="AssetTypeEnum.股票"
          placeholder="添加股票"
          ref="instrumentInputRef"
        />
      </div>
      <div flex aic>
        <el-tooltip effect="light" content="下载导入模板" placement="top">
          <i cursor-pointer mr-10 block fs-18 i-mdi-download @click="downloadTemplate"></i>
        </el-tooltip>
        <el-button color="var(--g-primary)" :loading="isImporting" @click="handleImport">
          <i mr-4 fs-14 i-mdi-import />
          导入
        </el-button>
        <el-button color="var(--g-red)" @click="handleClear">
          <i mr-4 fs-14 i-mdi-trash-can-outline />
          清空
        </el-button>
      </div>
    </div>

    <VirtualizedTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :row-actions="rowActions"
      show-index
    />
  </div>
</template>

<style scoped></style>
