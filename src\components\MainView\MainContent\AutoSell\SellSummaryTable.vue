<script setup lang="tsx">
import { onBeforeUnmount, onMounted, shallowRef } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { ColumnDefinition, TickTradeSummary } from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';

let interval = 0;

// 响应式数据
const trades = shallowRef<TickTradeSummary[]>([]);

// 卖出汇总表格列定义
const tradeColumns: ColumnDefinition<TickTradeSummary> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'totalVolume',
    dataKey: 'totalVolume',
    title: '成交总量',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'averagePrice',
    dataKey: 'averagePrice',
    title: '成交均价',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{Utils.formatNumber(cellData, { fix: 2 })}</span>,
  },
  {
    key: 'openPrice',
    dataKey: 'openPrice',
    title: '开盘价',
    width: 200,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { fix: 2, default: '--' })}</span>
    ),
  },
];

// 获取成交汇总
const fetchTrades = async () => {
  const { errorCode, data } = await RecordService.getTrades();
  if (errorCode === 0 && data) {
    trades.value = data;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTrades();
  interval = window.setInterval(fetchTrades, 5000);
});

onBeforeUnmount(() => {
  clearInterval(interval);
});
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>卖出汇总</span>
    </div>
    <div flex="~ 1" min-h-0>
      <VirtualizedTable w-full :data="trades" :columns="tradeColumns" />
    </div>
  </div>
</template>

<style scoped></style>
