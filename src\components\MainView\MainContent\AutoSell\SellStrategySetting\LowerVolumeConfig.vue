<script setup lang="ts">
import type { SellSetting } from '@/types';

const { modelValue, disabled } = defineProps<{
  modelValue: SellSetting['lowerVolume'];
  disabled?: boolean;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: SellSetting['lowerVolume']];
}>();

// 更新数据
const updateValue = (key: keyof SellSetting['lowerVolume'], subKey: string, value: any) => {
  const newValue = { ...modelValue };
  (newValue[key] as any)[subKey] = value;
  emit('update:modelValue', newValue);
};

// 时间格式化
const timeFormat = 'HH:mm:ss';

// 配置项定义
const configItems: Array<{
  key: keyof SellSetting['lowerVolume']['priority'];
  label: string;
  type: 'time' | 'number';
  suffix?: string;
  min?: number;
  max?: number;
}> = [
  { key: 'startTime', label: '开始时间', type: 'time' },
  { key: 'endTime', label: '结束时间', type: 'time' },
  { key: 'positionPercent', label: '卖出仓位', type: 'number', min: 0, max: 100, suffix: '%' },
  { key: 'lineUpVolumeAmount', label: '1 封单金额少于', suffix: '亿', type: 'number', min: 0 },
  { key: 'lineUpVolumeAmount1', label: '2 封单金额少于', suffix: '亿', type: 'number', min: 0 },
  { key: 'time', label: '而且', suffix: '毫秒', type: 'number', min: 0 },
  { key: 'downRate', label: '封单下降', suffix: '%', type: 'number', min: 0, max: 100 },
];

const configTypes: Array<{
  key: keyof SellSetting['lowerVolume'];
  label: string;
}> = [
  { key: 'global', label: '全局' },
  { key: 'priority', label: '优先' },
];
</script>

<template>
  <div flex="~ row" gap-20 :class="{ 'opacity-50 pointer-events-none': disabled }">
    <div v-for="type in configTypes" :key="type.key" flex="~ col 1">
      <div font-medium mb-2>{{ type.label }}</div>
      <div grid="~ cols-2" gap-10>
        <div flex aic gap-10 v-for="item in configItems" :key="item.key">
          <label>{{ item.label }}</label>
          <div flex-1 min-w-1 v-if="item.type === 'time'">
            <ElTimePicker
              style="width: 100%"
              :model-value="modelValue[type.key][item.key]!"
              :format="timeFormat"
              :value-format="timeFormat"
              placeholder="选择时间"
              size="small"
              @update:model-value="val => updateValue(type.key, item.key, val)"
            />
          </div>
          <div flex-1 min-w-1 v-else>
            <ElInputNumber
              style="width: 100%"
              :model-value="modelValue[type.key][item.key]"
              :min="item.min"
              :max="item.max"
              size="small"
              @update:model-value="val => updateValue(type.key, item.key, val)"
            >
              <template #suffix>{{ item.suffix }}</template>
            </ElInputNumber>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
