import type { InstrumentInfo } from '@/types';
import http from './http';
import { createLogger } from '../libs/logger';

const logger = createLogger('MarketService');

class MarketService {
  /**
   * 下载合约数据
   * @param type - 资产类型
   */
  static downloadInstruments(type: number) {
    logger.debug('下载合约数据', { type });
    return http<InstrumentInfo[]>('/v3/instrument', {
      method: 'GET',
      params: {
        type,
      },
    });
  }
}

export default MarketService;
