import type { TacticStatusEnum } from '@/enum';
import { ipc<PERSON>enderer } from 'electron';

export interface IpcRenderer {
  on: typeof ipcRenderer.on;
  emit: typeof ipcRenderer.emit;
  off: typeof ipcRenderer.off;
  send: typeof ipcRenderer.send;
  invoke: typeof ipcRenderer.invoke;
}

// 日志写入参数接口
export interface WriteLogParams {
  message: string;
  maxFileSize?: number;
  maxFiles?: number;
}

// 数据同步相关接口
export interface DataSyncAPI {
  notifyPoolStatusChanged: (poolId: number, status: TacticStatusEnum) => void;
  notifyPoolDetailStatusChanged: (detailId: number, status: any) => void;
  onPoolStatusChanged: (
    callback: (data: { poolId: number; status: TacticStatusEnum }) => void,
  ) => void;
  onPoolDetailStatusChanged: (
    callback: (data: { detailId: number; status: TacticStatusEnum }) => void,
  ) => void;
}
