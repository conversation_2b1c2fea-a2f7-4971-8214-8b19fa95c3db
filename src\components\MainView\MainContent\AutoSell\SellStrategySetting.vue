<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { TablePositionInfo, SellSetting } from '@/types';
import { RecordService } from '@/api';
import { BuyLevelsEnum } from '@/enum';
import LowerVolumeConfig from './SellStrategySetting/LowerVolumeConfig.vue';
import ProfitLossConfig from './SellStrategySetting/ProfitLossConfig.vue';
import TimedConfig from './SellStrategySetting/TimedConfig.vue';
import { Utils } from '@/script';
import { createLogger } from '@/libs/logger';

const logger = createLogger('SellStrategySetting');

const { selectedPosition } = defineProps<{
  selectedPosition: TablePositionInfo | null;
}>();

const emit = defineEmits<{
  strategySaved: [];
}>();

const defaultFormData: SellSetting = {
  openLower: false,
  openProfitLoss: false,
  openTimed: false,
  lowerVolume: {
    priority: {
      startTime: '',
      endTime: '',
      positionPercent: null,
      lineUpVolumeAmount: null,
      lineUpVolumeAmount1: null,
      time: null,
      downRate: null,
    },
    global: {
      startTime: '',
      endTime: '',
      positionPercent: null,
      lineUpVolumeAmount: null,
      lineUpVolumeAmount1: null,
      time: null,
      downRate: null,
    },
  },
  profitLoss: {
    open: false,
    time: null,
    pricePercent: null,
    takeProfit: [
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
    ],
    StopLoss: [
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
      { amplitude: null, positionPercent: null, open: false },
    ],
  },
  timed: {
    strategyDelayTime: null,
    strategyVolume: null,
    cancelProtectedTime: null,
    positionPercent: null,
    priceFollowType: BuyLevelsEnum.买一,
  },
};

// 表单数据
const formData = ref<SellSetting>(Utils.deepClone(defaultFormData));

// 原始数据备份，用于取消时还原
const originalData = ref<SellSetting | null>(null);

// 是否有选中的持仓
const hasSelectedPosition = computed(() => selectedPosition !== null);

// 当前持仓的策略ID
const currentStrategyId = computed(() => selectedPosition?.sellStrategy?.id || null);

const selectedPositionTitle = computed(() => {
  if (!selectedPosition) return '';
  return `${selectedPosition?.instrumentName}(${selectedPosition.instrument})`;
});

// 监听选中持仓变化，更新表单数据
watch(
  () => selectedPosition,
  newPosition => {
    if (newPosition) {
      if (newPosition.sellStrategy) {
        // 如果有策略配置，使用策略配置
        formData.value = Utils.deepClone(newPosition.sellStrategy.setting);
        originalData.value = Utils.deepClone(newPosition.sellStrategy.setting);
      } else {
        // 如果没有策略配置，使用默认配置
        resetToDefault();
      }
    }
  },
  { immediate: true },
);

// 重置为默认配置
const resetToDefault = () => {
  formData.value = Utils.deepClone(defaultFormData);
  originalData.value = Utils.deepClone(formData.value);
};

// 取消操作
const handleCancel = () => {
  if (originalData.value) {
    formData.value = Utils.deepClone(originalData.value);
  }
};

// 保存操作
const handleSave = async () => {
  if (!selectedPosition) {
    ElMessage.warning('请先选择持仓');
    return;
  }

  try {
    const saveData = {
      instrument: selectedPosition.instrument,
      instrumentName: selectedPosition.instrumentName,
      setting: formData.value,
    };

    // 如果持仓的setting不为null，则传id进行更新
    if (currentStrategyId.value) {
      await RecordService.saveSellStrategy({ ...saveData, id: currentStrategyId.value });
    } else {
      await RecordService.saveSellStrategy(saveData);
    }

    ElMessage.success('保存成功');
    originalData.value = Utils.deepClone(formData.value);
    emit('strategySaved');
  } catch (error) {
    logger.error('保存卖出策略失败:', error);
    ElMessage.error('保存失败');
  }
};
</script>

<template>
  <div flex="~ col" h-full>
    <div bg="[--g-panel-bg]" h-32 flex aic px-16 jcsb border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>卖出策略 &nbsp;&nbsp;&nbsp;{{ selectedPositionTitle }}</span>

      <div v-if="hasSelectedPosition">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton color="var(--g-primary)" @click="handleSave">保存</ElButton>
      </div>
    </div>

    <div v-if="!hasSelectedPosition" flex="~ 1" items-center justify-center>
      <span>请选择持仓</span>
    </div>

    <div v-else flex="~ col 1" min-h-0>
      <!-- 策略配置 -->
      <div flex="~ col 1" min-h-0 p-16 overflow-y-auto>
        <!-- 低封单量策略 -->
        <div flex="~ col" gap-4 mb-6>
          <div flex="~ none" items-center gap-2>
            <ElCheckbox v-model="formData.openLower">低封单量</ElCheckbox>
          </div>
          <LowerVolumeConfig v-model="formData.lowerVolume" :disabled="!formData.openLower" />
        </div>

        <!-- 分割线 -->
        <div w-full h-1 border-b="1 solid [--el-border-color]" my-10></div>

        <!-- 止盈止损策略 -->
        <div flex="~ col" gap-4 mb-6>
          <div flex="~ none" items-center gap-2>
            <ElCheckbox v-model="formData.openProfitLoss">止盈止损</ElCheckbox>
          </div>
          <ProfitLossConfig v-model="formData.profitLoss" :disabled="!formData.openProfitLoss" />
        </div>

        <!-- 分割线 -->
        <div w-full h-1 border-b="1 solid [--el-border-color]" my-10></div>

        <!-- 定时定量策略 -->
        <div flex="~ col" gap-4 mb-6>
          <div flex="~ none" items-center gap-2>
            <ElCheckbox v-model="formData.openTimed">定时定量</ElCheckbox>
          </div>
          <TimedConfig v-model="formData.timed" :disabled="!formData.openTimed" />
        </div>
      </div>
    </div>
  </div>
</template>
