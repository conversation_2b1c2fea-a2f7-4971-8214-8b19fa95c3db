import { createRouter, createWebHashHistory } from 'vue-router';
import Misc from '../script/misc';
import { createLogger } from '../libs/logger';

const logger = createLogger('Router');
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'main',
      component: () => import('../views/MainView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
    {
      path: '/floating',
      name: 'floating',
      component: () => import('../views/FloatingWindow.vue'),
    },
  ],
});

router.beforeEach((to, from, next) => {
  if (import.meta.env.DEV) {
    logger.debug('路由导航:', { from: from.path, to: to.path });
  }
  // 悬浮窗口页面不需要登录验证
  if (to.name === 'floating') {
    next();
    return;
  }
  // 未登录跳转到登录页
  if (!Misc.loggedIn() && to.name !== 'login') {
    logger.info('用户未登录，跳转到登录页');
    next({ name: 'login' });
  } else {
    next();
  }
});

export default router;
