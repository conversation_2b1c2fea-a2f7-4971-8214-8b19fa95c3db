import { SendFunctionCode, ReceiveFunctionCode } from '@/enum';
import type { WsPool, WsPoolDetail } from './pool';

interface CommonWsResponse {
  errorCode: number;
  errorMsg?: string;
}

export interface LoginResponse extends CommonWsResponse {
  fullName: string;
  orgId: number;
  orgName: string;
  roleId: number;
  token: string;
  userId: number;
  userType: number;
  username: string;
}

export interface LoginBody {
  loginType: number;
  branchId: string;
  orgId: string;
  credit: number;
  username: string;
  password: string;
  mac: string;
  os: string;
  md5: string;
  sessionId: string;
  configStr: string;
}

export interface TokenResponse {
  token: string;
}

export type FunctionCodeBody = {
  [SendFunctionCode.登录]: LoginBody;
  [SendFunctionCode.请求TOKEN]: undefined;
  [SendFunctionCode.心跳]: undefined;
  [SendFunctionCode.登出]: undefined;
  [ReceiveFunctionCode.登录响应]: LoginResponse;
  [ReceiveFunctionCode.请求TOKEN相应]: TokenResponse;
  [ReceiveFunctionCode.新增策略池推送]: WsPool<0>;
  [ReceiveFunctionCode.更新策略池推送]: WsPool<0>;
  [ReceiveFunctionCode.删除策略池推送]: WsPool<1>;
  [ReceiveFunctionCode.策略池明细变更推送]: WsPoolDetail;
  // [ReceiveFunctionCode.卖单策略变更推送]: any;
};

/**
 * WebSocket 消息接口
 * @template T 消息体类型
 */
export interface WsMessage<T extends SendFunctionCode | ReceiveFunctionCode> {
  fc: T;
  reqId: ReqId;
  dataType: number;
  body?: FunctionCodeBody[T];
}

/** 请求ID 0-65535 */
export type ReqId = number;

/** 消息处理器映射 */
export type HandlersMap<T extends SendFunctionCode> = Map<
  ReqId,
  (data: FunctionCodeBody[T]) => void
>;

export type EventDetail<T extends ReceiveFunctionCode> = {
  fc: T;
  body: FunctionCodeBody[T];
};
