import type {} from '@/types';
import http from './http';
import type { RiskStockTypeEnum } from '@/enum';
import type { RiskStock, RiskControl } from '@/types';

class RiskService {
  static base = '/v5/pool';

  /**
   * 查询股票池列表
   * @param listType - 列表类型 1 黑名单 2 重点监控
   */
  static getStockList(list_type: RiskStockTypeEnum) {
    return http<RiskStock[]>(`${this.base}/list`, {
      params: {
        list_type,
      },
    });
  }

  /**
   * 查询风控设置
   */
  static getRiskControl() {
    return http<RiskControl>(`${this.base}/risk`);
  }

  /**
   * 新增股票到列表
   * @param listType - 列表类型
   * @param stock - 股票信息
   */
  static addStock(list_type: RiskStockTypeEnum, data: Omit<RiskStock, 'id'>) {
    return http<void>(`${this.base}/list`, {
      method: 'POST',
      params: {
        list_type,
      },
      data,
    });
  }

  /**
   * 新增或更新风控设置
   * @param risk - 风控设置
   */
  static updateRiskControl(data: RiskControl) {
    return http<void>(`${this.base}/risk`, {
      method: 'POST',
      data,
    });
  }

  /**
   * 批量新增股票到列表
   * @param listType - 列表类型
   * @param stocks - 股票列表
   */
  static batchAddStocks(list_type: RiskStockTypeEnum, data: Omit<RiskStock, 'id'>[]) {
    return http<void>(`${this.base}/list/batch`, {
      method: 'POST',
      params: {
        list_type,
      },
      data,
    });
  }

  /**
   * 根据类型清空股票列表
   * @param listType - 列表类型
   */
  static deleteStockList(list_type: RiskStockTypeEnum) {
    return http<void>(`${this.base}/list`, {
      method: 'DELETE',
      params: {
        list_type,
      },
    });
  }

  /**
   * 移除单个股票
   * @param id - 股票ID
   */
  static removeStock(id: number) {
    return http<void>(`${this.base}/list/item`, {
      method: 'DELETE',
      params: {
        id,
      },
    });
  }
}

export default RiskService;
