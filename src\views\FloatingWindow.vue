<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import type { Pool, PoolDetail } from '@/types';
import { isStopped } from '@/enum';
import { createLogger } from '@/libs/logger';

const logger = createLogger('FloatingWindow');

// 响应式数据
const currentStockCode = ref<string>('');
const allPools = ref<Pool[]>([]);
const allPoolDetails = ref<PoolDetail[]>([]);

const currentStockDetail = computed(() => {
  return allPoolDetails.value.find(detail => detail.instrument === currentStockCode.value) || null;
});
const canStart = computed(() => currentStockDetail.value && isStopped(currentStockDetail.value));
const canStop = computed(() => currentStockDetail.value && !isStopped(currentStockDetail.value));

// 启动股票自动买入
const startStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;
  window.ipcRenderer.send('start-pool-detail', currentStockDetail.value);
};

// 停止股票自动买入
const stopStockAutoBuy = async () => {
  if (!currentStockDetail.value) return;
  window.ipcRenderer.send('stop-pool-detail', currentStockDetail.value);
};

// 将股票加入股票池
const addStockToPool = async (pool: Pool) => {
  if (!currentStockCode.value) return;
  window.ipcRenderer.send('add-pool-detail', {
    instrument: currentStockCode.value,
    pool,
  });
};

// 从主窗口接收股票池数据
const handlePoolsDataUpdated = (_: Electron.IpcRendererEvent, pools: Pool[]) => {
  allPools.value = pools;
  logger.info('接收到股票池数据更新', { poolCount: pools.length });
};

const handlePoolDetailsDataUpdated = (_: Electron.IpcRendererEvent, poolDetails: PoolDetail[]) => {
  allPoolDetails.value = poolDetails;
  logger.info('接收到股票池详情数据更新', { detailCount: poolDetails.length });
};

// 处理股票代码捕获
const handleStockCodeCaptured = (_: Electron.IpcRendererEvent, stockCode: string) => {
  logger.info('捕获到股票代码', { stockCode });
  currentStockCode.value = stockCode;
};

onMounted(() => {
  // 监听股票代码捕获事件
  window.ipcRenderer.on('stock-code-captured', handleStockCodeCaptured);
  // 监听数据同步事件
  window.ipcRenderer.on('pools-data-updated', handlePoolsDataUpdated);
  window.ipcRenderer.on('pool-details-data-updated', handlePoolDetailsDataUpdated);
  // 获取全量数据
  window.ipcRenderer.send('sync-all-data');
});

onUnmounted(() => {
  // 取消监听股票代码捕获事件
  window.ipcRenderer.off('stock-code-captured', handleStockCodeCaptured);
  // 取消监听数据同步事件
  window.ipcRenderer.off('pools-data-updated', handlePoolsDataUpdated);
  window.ipcRenderer.off('pool-details-data-updated', handlePoolDetailsDataUpdated);
});
</script>

<template>
  <div class="floating-window" flex aic gap-10 px-20>
    <div class="no-drag">
      {{ currentStockCode || '--' }}
    </div>
    <el-button
      @click="startStockAutoBuy"
      :disabled="!canStart"
      :class="{ 'cursor-not-allowed': !canStart }"
      class="no-drag"
    >
      <i mr-4 fs-14 i-mdi-motion-play-outline />
      启动
    </el-button>
    <el-button
      @click="stopStockAutoBuy"
      :disabled="!canStop"
      :class="{ 'cursor-not-allowed': !canStop }"
      class="no-drag"
    >
      <i mr-4 fs-14 i-mdi-motion-pause-outline />
      停止
    </el-button>
    <el-button
      :type="currentStockDetail?.poolId === pool.id ? 'primary' : ''"
      v-for="pool in allPools"
      :key="pool.id"
      :disabled="!currentStockCode || !!currentStockDetail"
      class="no-drag"
      @click="addStockToPool(pool)"
    >
      {{ pool.groupName }}
    </el-button>
  </div>
</template>

<style scoped>
.floating-window {
  -webkit-app-region: drag;
  cursor: pointer;
  .no-drag {
    -webkit-app-region: no-drag;
  }
}
</style>
